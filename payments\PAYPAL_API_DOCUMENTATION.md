# PayPal Payment Integration API Documentation

## Overview
Complete PayPal payment integration with sandbox and production support. The system handles the full payment flow from initiation to completion with proper error handling and status tracking.

## Configuration

### Environment Settings
The system supports both sandbox and production environments:

```python
# In your Django settings.py
PAYPAL_USE_SANDBOX = True  # Set to False for production

# Optional: Override default credentials
PAYPAL_SANDBOX_CLIENT_ID = "your_sandbox_client_id"
PAYPAL_SANDBOX_CLIENT_SECRET = "your_sandbox_client_secret"
PAYPAL_PRODUCTION_CLIENT_ID = "your_production_client_id"
PAYPAL_PRODUCTION_CLIENT_SECRET = "your_production_client_secret"
```

### Default Credentials
- **Sandbox Client ID**: `AQVLaiTjLhAyMbzIkOv9XXf155XQbpEfCpebybwaaYFnPO8cc_cUnnNgY2KlGsa2Ta8KJTLPOmXJr3QH`
- **Sandbox Client Secret**: `EEOzergzcswVKKhA4seInDPsNUCT1w-tYh3_GAD9EHOPax9xnq0ICjJpLMG1F63GqgbnSJ33Mr15GAuM`

## API Endpoints

### 1. Initiate PayPal Payment

**Endpoint**: `POST /api/payments/paypal/pay/`

**Description**: Creates a PayPal order and returns the approval URL for frontend redirection.

**Authentication**: Required

**Request Body**:
```json
{
    "payment_id": 123,
    "frontend_base_url": "https://yourfrontend.com"  // Optional, defaults to localhost:3000
}
```

**Response (Success - 201)**:
```json
{
    "message": "PayPal order created successfully",
    "data": {
        "payment_id": 123,
        "amount": 45.00,
        "approval_url": "https://www.sandbox.paypal.com/checkoutnow?token=ORDER_ID",
        "order_id": "ORDER_ID_FROM_PAYPAL",
        "description": "Membership Payment for 1 member(s)"
    },
    "status_code": 201
}
```

**Response (Error - 400/404/500)**:
```json
{
    "message": "Error description",
    "data": null,
    "status_code": 400
}
```

**Frontend Integration**:
```javascript
// Frontend code example
const initiatePayment = async (paymentId) => {
    const response = await fetch('/api/payments/paypal/pay/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer YOUR_TOKEN'
        },
        body: JSON.stringify({
            payment_id: paymentId,
            frontend_base_url: window.location.origin
        })
    });
    
    const data = await response.json();
    
    if (response.ok) {
        // Redirect user to PayPal
        window.location.href = data.data.approval_url;
    } else {
        console.error('Payment initiation failed:', data.message);
    }
};
```

### 2. PayPal Success Callback

**Endpoint**: `GET /api/payments/paypal/success/`

**Description**: Handles successful PayPal payment callback. PayPal redirects here after user approves payment.

**Authentication**: Not required (PayPal callback)

**Query Parameters**:
- `payment_id`: The original payment ID
- `token`: PayPal order ID
- `PayerID`: PayPal payer ID

**Response**: HTTP redirect to frontend success page

**Redirect URL**: `{your_domain}/payment-success?payment_id={id}&transaction_id={transaction_id}`

### 3. PayPal Cancel Callback

**Endpoint**: `GET /api/payments/paypal/cancel/`

**Description**: Handles cancelled PayPal payment callback. PayPal redirects here if user cancels payment.

**Authentication**: Not required (PayPal callback)

**Query Parameters**:
- `payment_id`: The original payment ID
- `token`: PayPal order ID (optional)

**Response**: HTTP redirect to frontend cancel page

**Redirect URL**: `{your_domain}/payment-cancelled?payment_id={id}`

## Payment Flow

### Complete Payment Process

1. **Frontend initiates payment**:
   ```
   POST /api/payments/paypal/pay/
   ```

2. **Backend creates PayPal order**:
   - Validates payment exists
   - Creates PayPal order via API
   - Updates payment record with PayPal order ID
   - Returns approval URL

3. **Frontend redirects to PayPal**:
   ```
   window.location.href = approval_url
   ```

4. **User completes payment on PayPal**

5. **PayPal redirects to success/cancel URL**:
   - Success: `/api/payments/paypal/success/`
   - Cancel: `/api/payments/paypal/cancel/`

6. **Backend processes callback**:
   - Captures payment (success only)
   - Updates payment status
   - Extracts transaction details
   - Redirects to frontend

7. **Frontend handles final redirect**:
   - Success page shows transaction details
   - Cancel page allows retry

### Payment Status Flow

```
PENDING → (PayPal approval) → SUCCESS
PENDING → (User cancels) → CANCELLED
PENDING → (PayPal error) → FAILED
```

## Error Handling

### Common Error Scenarios

1. **Payment Not Found (404)**:
   ```json
   {
       "message": "Payment not found",
       "status_code": 404
   }
   ```

2. **PayPal API Error (400)**:
   ```json
   {
       "message": "PayPal error: Invalid request",
       "status_code": 400
   }
   ```

3. **Configuration Error (500)**:
   ```json
   {
       "message": "PayPal credentials not configured",
       "status_code": 500
   }
   ```

### Frontend Error Handling

```javascript
const handlePaymentError = (error) => {
    switch (error.status_code) {
        case 404:
            alert('Payment not found. Please try again.');
            break;
        case 400:
            alert('Payment error: ' + error.message);
            break;
        case 500:
            alert('System error. Please contact support.');
            break;
        default:
            alert('Unexpected error occurred.');
    }
};
```

## Security Considerations

### Authentication
- Payment initiation requires user authentication
- Callback URLs are public (PayPal requirement)
- Payment validation occurs on backend

### Data Protection
- PayPal credentials stored securely
- Transaction IDs logged for audit
- Payment status changes tracked

### Validation
- Payment ownership validated
- Amount verification with PayPal
- Duplicate transaction prevention

## Testing

### Sandbox Testing
1. Set `PAYPAL_USE_SANDBOX = True`
2. Use sandbox credentials
3. Test with PayPal sandbox accounts

### Test Scenarios
- Successful payment completion
- User cancellation
- PayPal API errors
- Network timeouts
- Invalid payment IDs

## Monitoring and Logging

### Log Events
- Payment initiation attempts
- PayPal API calls and responses
- Successful payment captures
- Error conditions and failures

### Metrics to Track
- Payment success rate
- Average payment completion time
- Error frequency by type
- PayPal fee amounts

## Production Deployment

### Checklist
- [ ] Set `PAYPAL_USE_SANDBOX = False`
- [ ] Configure production PayPal credentials
- [ ] Update frontend redirect URLs
- [ ] Test with small amounts first
- [ ] Monitor error logs
- [ ] Set up payment reconciliation

### Environment Variables
```bash
PAYPAL_USE_SANDBOX=False
PAYPAL_PRODUCTION_CLIENT_ID=your_production_client_id
PAYPAL_PRODUCTION_CLIENT_SECRET=your_production_client_secret
```

## Support and Troubleshooting

### Common Issues
1. **"PayPal credentials not configured"**: Check environment settings
2. **"Failed to get PayPal approval URL"**: Verify PayPal API connectivity
3. **Payment stuck in PENDING**: Check PayPal callback URLs
4. **Duplicate payments**: Implement frontend payment button disabling

### Debug Mode
Enable detailed logging by setting Django log level to DEBUG for the `payments` app.

---

**Note**: This implementation maintains full backward compatibility with existing payment APIs while adding the new PayPal flow.
