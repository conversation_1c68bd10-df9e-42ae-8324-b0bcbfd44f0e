"""
Payment model for handling membership and event registration payments.
"""
from datetime import datetime
from django.db import models, transaction
from django.db.models import Max
from django.utils import timezone
from django.core.exceptions import ValidationError
from simple_history.models import HistoricalRecords

from payments.models.invoice_due_date import InvoiceDueDate
from payments.models.validators import validate_invoice_format


class Payment(models.Model):
    """
    Payment model for handling both membership and event registration payments
    """
    class PaymentStatus(models.TextChoices):
        """Defines the possible payment statuses"""
        SUCCESS = 'success', 'Success'
        PENDING = 'pending', 'Pending'
        REFUND = 'refund', 'Refund'
        FAILED = 'failed', 'Failed'
        ADJUSTMENT = 'adjustment', 'Adjustment'

    class PaymentType(models.TextChoices):
        """Defines the possible payment types"""
        PREPAID = 'prepaid', 'Prepaid'
        COLLECT = 'collect', 'Collect'
        CHECKS = 'checks', 'Checks'
        CASH = 'cash', 'Cash'
        PAYPAL = 'paypal', 'PayPal'
        MONEY_ORDER = 'money_order', 'Money Order'

    class PaymentFor(models.TextChoices):
        """Defines what the payment is for"""
        MEMBERSHIP = 'membership', 'Membership'
        EVENT = 'event', 'Event Registration'

    payer = models.ForeignKey(
        'core.Member',
        on_delete=models.CASCADE,
        related_name='payer_payments')
    covered_members = models.ManyToManyField(
        'core.Member', related_name='covered_payments')
    amount = models.DecimalField(max_digits=10, decimal_places=2,
                               help_text="Per-member amount before multiplication")
    total_amount = models.DecimalField(max_digits=10, decimal_places=2,
                                     null=True, blank=True,
                                     help_text="Total amount after multiplying by covered members count")
    invoice_number = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        validators=[validate_invoice_format],
        help_text="Format: YYDDD-XXX (year+julian_date-sequence)"
    )
    po_number = models.CharField(max_length=100, blank=True, null=True)
    paid_year = models.IntegerField(default=timezone.now().year)
    paid_next_year = models.CharField(max_length=100, blank=True, null=True)
    payment_link = models.URLField(max_length=200, blank=True, null=True)
    payment_id = models.CharField(max_length=365, blank=True, null=True)
    payment_date = models.DateField(blank=True, null=True)
    date = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)
    status = models.CharField(
        max_length=20,
        choices=PaymentStatus.choices,
        default=PaymentStatus.PENDING
    )
    notes = models.TextField(blank=True, null=True)
    payment_type = models.CharField(
        max_length=20,
        choices=PaymentType.choices,
        blank=True,
        null=True,
        default=""
    )
    draft = models.BooleanField(default=True)
    billing_address = models.CharField(max_length=255, blank=True, default="")
    transaction_id = models.CharField(max_length=255, blank=True, null=True)
    paypal_response = models.JSONField(blank=True, null=True)
    due_date = models.DateField()

    # PayPal specific fields
    paypal_order_id = models.CharField(max_length=255, blank=True, null=True)
    paypal_payer_id = models.CharField(max_length=255, blank=True, null=True)
    paypal_payment_id = models.CharField(max_length=255, blank=True, null=True)
    paypal_fee = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    paypal_payment_status = models.CharField(max_length=100, blank=True, null=True)
    paypal_payment_method = models.CharField(max_length=100, blank=True, null=True)

    # Fields for handling both membership and event registration payments
    payment_for = models.CharField(
        max_length=20,
        choices=PaymentFor.choices,
        default=PaymentFor.MEMBERSHIP
    )
    event_registration = models.ForeignKey(
        'core.EventRegistration',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='payments'
    )

    history = HistoricalRecords()

    @staticmethod
    def generate_invoice_number_today() -> str:
        """
        Generates a unique invoice number using atomic transaction to prevent race conditions
        Format: YYDDD-XXX where YYDDD=year+julian date, XXX=sequential number
        """
        today_date = datetime.now()
        year_short = today_date.strftime("%y")
        julian_date = today_date.strftime("%j").zfill(3)
        prefix = f"{year_short}{julian_date}"

        with transaction.atomic():
            # Lock the table to prevent concurrent access
            # pylint: disable=no-member
            last_payment = Payment.objects.select_for_update().filter(
                invoice_number__startswith=prefix
            ).order_by('-invoice_number').first()
            # pylint: enable=no-member

            if last_payment and last_payment.invoice_number:
                try:
                    counter = int(
                        last_payment.invoice_number.split('-')[1]) + 1
                except (IndexError, ValueError):
                    counter = 1
            else:
                counter = 1

            return f"{prefix}-{counter:03d}"

    @staticmethod
    def generate_event_invoice_number_today() -> str:
        """
        Generates a unique event registration invoice number using atomic transaction
        Format: EVE-YYDDD-XXX where YY=year, DDD=julian date, XXX=sequential number
        """
        today_date = datetime.now()
        year_short = today_date.strftime("%y")
        julian_date = today_date.strftime("%j").zfill(3)
        prefix = f"EVE-{year_short}{julian_date}"

        with transaction.atomic():
            # Lock the table to prevent concurrent access
            # pylint: disable=no-member
            last_payment = Payment.objects.select_for_update().filter(
                invoice_number__startswith=prefix,
                payment_for=Payment.PaymentFor.EVENT
            ).order_by('-invoice_number').first()
            # pylint: enable=no-member

            if last_payment and last_payment.invoice_number:
                try:
                    counter = int(
                        last_payment.invoice_number.split('-')[2]) + 1
                except (IndexError, ValueError):
                    counter = 1
            else:
                counter = 1

            return f"{prefix}-{counter:03d}"

    @staticmethod
    def get_last_invoice_number_for_month(year: int, month: int) -> str:
        """
        Get the last invoice number for a specific month and year
        """
        start_date = timezone.make_aware(datetime(year, month, 1))
        if month == 12:
            end_date = timezone.make_aware(datetime(year + 1, 1, 1))
        else:
            end_date = timezone.make_aware(datetime(year, month + 1, 1))

        # pylint: disable=no-member
        last_payment = Payment.objects.filter(
            date__gte=start_date,
            date__lt=end_date,
            invoice_number__isnull=False
        ).aggregate(max_invoice_number=Max('invoice_number'))
        # pylint: enable=no-member

        return last_payment['max_invoice_number']

    def save(self, *args, **kwargs):
        """
        Override save method to handle invoice number generation, related payments updates,
        and calculate total_amount based on amount and covered members count
        """
        is_new = self.pk is None

        # For new payments, handle invoice number and due date
        if is_new:
            if self.invoice_number is None:
                if self.payment_for == self.PaymentFor.EVENT:
                    self.invoice_number = self.generate_event_invoice_number_today()
                else:
                    self.invoice_number = self.generate_invoice_number_today()
            if not self.due_date:
                # Get the first available due date from InvoiceDueDate model
                latest_due_date = InvoiceDueDate.objects.order_by('-created').first()
                if latest_due_date:
                    self.due_date = latest_due_date.due_date

            # If this is an event payment, get the amount from the event registration
            if (self.payment_for == self.PaymentFor.EVENT and
                    self.event_registration and not self.amount):
                self.amount = self.event_registration.total_amount

            # Set default total_amount if not provided
            if self.total_amount is None:
                self.total_amount = self.amount
        else:
            # For existing payments, handle related payments updates
            try:
                original = Payment.objects.get(pk=self.pk)
                # Check if status or payment_type has changed
                if (original.status != self.status or
                        original.payment_type != self.payment_type):
                    # Check if this is part of a group payment
                    related_payments = Payment.objects.filter(
                        invoice_number=self.invoice_number
                    ).exclude(pk=self.pk)
                    if related_payments.exists():
                        # Update all related payments with same invoice number
                        related_payments.update(
                            status=self.status,
                            payment_type=self.payment_type
                        )
            except Payment.DoesNotExist:
                pass

        # Save the object first to ensure we can access M2M relationships
        super().save(*args, **kwargs)

        # After saving, update total_amount based on covered members count
        # But only for new payments - for updates, the serializer handles this
        if self.pk and is_new:  # Only for new payments that have been saved
            covered_members_count = self.covered_members.count()
            if covered_members_count > 0:
                expected_total_amount = self.amount * covered_members_count
                if self.total_amount != expected_total_amount:
                    # Use update to avoid infinite recursion
                    Payment.objects.filter(pk=self.pk).update(total_amount=expected_total_amount)
                    # Update the instance
                    self.total_amount = expected_total_amount

    def __str__(self):
        """Return string representation of Payment"""
        payment_type = ("Event Registration" if self.payment_for ==
                        self.PaymentFor.EVENT else "Membership")
        return f"{payment_type} Payment {self.pk} by {self.payer}"