"""
Serializers for PayPal payment processing.
"""
from rest_framework import serializers
from payments.models.payment import Payment


class PayPalPaymentSerializer(serializers.Serializer):
    """Serializer for initiating PayPal payments"""
    payment_id = serializers.IntegerField(required=True)
    return_url = serializers.URLField(required=True)
    cancel_url = serializers.URLField(required=True)
    
    def validate(self, data):
        """Validate the payment exists"""
        try:
            payment = Payment.objects.get(pk=data.get('payment_id'))
            # Store payment in data for later use
            data['payment'] = payment
        except Payment.DoesNotExist:
            raise serializers.ValidationError({"payment_id": "Payment not found"})
            
        return data


class PayPalCaptureSerializer(serializers.Serializer):
    """Serializer for capturing PayPal payments after approval"""
    order_id = serializers.CharField(required=True)
    payment_id = serializers.IntegerField(required=True)
    
    def validate(self, data):
        """Validate the payment exists"""
        try:
            payment = Payment.objects.get(pk=data.get('payment_id'))
            # Store payment in data for later use
            data['payment'] = payment
        except Payment.DoesNotExist:
            raise serializers.ValidationError({"payment_id": "Payment not found"})
            
        return data
