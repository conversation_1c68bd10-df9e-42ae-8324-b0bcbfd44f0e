"""
Tests for PayPal integration functionality.
"""
from decimal import Decimal
from unittest.mock import patch, MagicMock
from django.test import TestCase, override_settings
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status

from core.models import Member
from payments.models import Payment
from payments.services import PayPalService
from payments.services.paypal_service import PayPalAPIError, PayPalConfig
from payments.config import PaymentConfig


class PayPalConfigTest(TestCase):
    """Test PayPal configuration"""
    
    @override_settings(PAYPAL_USE_SANDBOX=True)
    def test_sandbox_config(self):
        """Test sandbox configuration"""
        config = PayPalConfig.from_settings()
        
        self.assertEqual(config.client_id, PaymentConfig.PAYPAL_SANDBOX_CLIENT_ID)
        self.assertEqual(config.client_secret, PaymentConfig.PAYPAL_SANDBOX_CLIENT_SECRET)
        self.assertEqual(config.base_url, PaymentConfig.PAYPAL_SANDBOX_BASE_URL)
        self.assertTrue(config.is_sandbox)
    
    @override_settings(
        PAYPAL_USE_SANDBOX=False,
        PAYPAL_PRODUCTION_CLIENT_ID="test_prod_id",
        PAYPAL_PRODUCTION_CLIENT_SECRET="test_prod_secret"
    )
    def test_production_config(self):
        """Test production configuration"""
        config = PayPalConfig.from_settings()
        
        self.assertEqual(config.client_id, "test_prod_id")
        self.assertEqual(config.client_secret, "test_prod_secret")
        self.assertEqual(config.base_url, PaymentConfig.PAYPAL_PRODUCTION_BASE_URL)
        self.assertFalse(config.is_sandbox)


class PayPalServiceTest(TestCase):
    """Test PayPal service functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.service = PayPalService()
    
    @patch('payments.services.paypal_service.requests.request')
    def test_get_access_token_success(self, mock_request):
        """Test successful access token retrieval"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'access_token': 'test_token_123',
            'expires_in': 3600
        }
        mock_request.return_value = mock_response
        
        token = self.service.get_access_token()
        
        self.assertEqual(token, 'test_token_123')
        self.assertIsNotNone(self.service._access_token)
    
    @patch('payments.services.paypal_service.requests.request')
    def test_get_access_token_failure(self, mock_request):
        """Test access token retrieval failure"""
        mock_response = MagicMock()
        mock_response.status_code = 401
        mock_response.json.return_value = {'error': 'unauthorized'}
        mock_request.return_value = mock_response
        
        with self.assertRaises(PayPalAPIError):
            self.service.get_access_token()
    
    @patch.object(PayPalService, 'get_access_token')
    @patch('payments.services.paypal_service.requests.request')
    def test_create_order_success(self, mock_request, mock_token):
        """Test successful order creation"""
        mock_token.return_value = 'test_token'
        mock_response = MagicMock()
        mock_response.status_code = 201
        mock_response.json.return_value = {
            'id': 'ORDER_123',
            'links': [
                {'rel': 'approve', 'href': 'https://paypal.com/approve/ORDER_123'}
            ]
        }
        mock_request.return_value = mock_response
        
        order = self.service.create_order(
            amount=100.0,
            description='Test payment',
            reference_id='ref_123',
            return_url='https://example.com/success',
            cancel_url='https://example.com/cancel'
        )
        
        self.assertEqual(order['id'], 'ORDER_123')
        self.assertEqual(len(order['links']), 1)


class PayPalPaymentFlowTest(TestCase):
    """Test PayPal payment flow views"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.member = Member.objects.create(
            email='<EMAIL>',
            name='Test Member'
        )
        self.payment = Payment.objects.create(
            payer=self.member,
            amount=Decimal('45.00'),
            total_amount=Decimal('45.00'),
            payment_for=Payment.PaymentFor.MEMBERSHIP,
            status=Payment.PaymentStatus.PENDING
        )
        self.payment.covered_members.add(self.member)
    
    def test_paypal_pay_unauthenticated(self):
        """Test PayPal payment initiation without authentication"""
        url = reverse('paypal-pay')
        response = self.client.post(url, {
            'payment_id': self.payment.pk
        })
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_paypal_pay_missing_payment_id(self):
        """Test PayPal payment initiation without payment ID"""
        self.client.force_authenticate(user=self.member)
        url = reverse('paypal-pay')
        response = self.client.post(url, {})
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Payment ID is required', response.data['message'])
    
    def test_paypal_pay_payment_not_found(self):
        """Test PayPal payment initiation with invalid payment ID"""
        self.client.force_authenticate(user=self.member)
        url = reverse('paypal-pay')
        response = self.client.post(url, {
            'payment_id': 99999
        })
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    @patch('payments.views.paypal_views.PayPalService')
    def test_paypal_pay_success(self, mock_service_class):
        """Test successful PayPal payment initiation"""
        # Mock PayPal service
        mock_service = MagicMock()
        mock_service.create_order.return_value = {
            'id': 'ORDER_123',
            'links': [
                {'rel': 'approve', 'href': 'https://paypal.com/approve/ORDER_123'}
            ]
        }
        mock_service_class.return_value = mock_service
        
        self.client.force_authenticate(user=self.member)
        url = reverse('paypal-pay')
        response = self.client.post(url, {
            'payment_id': self.payment.pk
        })
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('approval_url', response.data['data'])
        self.assertEqual(response.data['data']['payment_id'], self.payment.pk)
        
        # Verify payment was updated
        self.payment.refresh_from_db()
        self.assertEqual(self.payment.payment_type, Payment.PaymentType.PAYPAL)
        self.assertEqual(self.payment.paypal_order_id, 'ORDER_123')
    
    def test_paypal_success_callback(self):
        """Test PayPal success callback"""
        self.payment.paypal_order_id = 'ORDER_123'
        self.payment.save()
        
        url = reverse('paypal-success')
        response = self.client.get(url, {
            'payment_id': self.payment.pk,
            'token': 'ORDER_123',
            'PayerID': 'PAYER_123'
        })
        
        # Should redirect (even if PayPal capture fails in test)
        self.assertEqual(response.status_code, 302)
    
    def test_paypal_cancel_callback(self):
        """Test PayPal cancel callback"""
        url = reverse('paypal-cancel')
        response = self.client.get(url, {
            'payment_id': self.payment.pk,
            'token': 'ORDER_123'
        })
        
        # Should redirect
        self.assertEqual(response.status_code, 302)
        
        # Verify payment status updated
        self.payment.refresh_from_db()
        self.assertEqual(self.payment.status, Payment.PaymentStatus.CANCELLED)


class PayPalIntegrationTest(TestCase):
    """Integration tests for PayPal payment flow"""
    
    def setUp(self):
        """Set up test data"""
        self.member = Member.objects.create(
            email='<EMAIL>',
            name='Test Member'
        )
        self.payment = Payment.objects.create(
            payer=self.member,
            amount=Decimal('45.00'),
            total_amount=Decimal('45.00'),
            payment_for=Payment.PaymentFor.MEMBERSHIP,
            status=Payment.PaymentStatus.PENDING
        )
        self.payment.covered_members.add(self.member)
    
    def test_payment_status_transitions(self):
        """Test payment status transitions"""
        # Initial status
        self.assertEqual(self.payment.status, Payment.PaymentStatus.PENDING)
        
        # Simulate PayPal order creation
        self.payment.payment_type = Payment.PaymentType.PAYPAL
        self.payment.paypal_order_id = 'ORDER_123'
        self.payment.save()
        
        # Simulate successful payment
        from payments.services import PaymentService
        PaymentService.update_payment_status(self.payment, Payment.PaymentStatus.SUCCESS)
        
        self.payment.refresh_from_db()
        self.assertEqual(self.payment.status, Payment.PaymentStatus.SUCCESS)
        self.assertIsNotNone(self.payment.payment_date)
    
    def test_payment_description_generation(self):
        """Test payment description generation"""
        from payments.services import PaymentService
        
        # Test membership payment description
        description = PaymentService.get_payment_description(self.payment)
        self.assertIn('Membership Payment', description)
        self.assertIn('1 member', description)
        
        # Test event payment description
        # Note: Would need EventRegistration model for full test
