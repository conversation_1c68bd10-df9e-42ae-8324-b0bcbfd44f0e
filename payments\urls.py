from django.urls import path

from payments.views import (
    PaymentListView, PaymentCreateView, PaymentDetailView,
    InitiatePayPalPaymentView, CapturePayPalPaymentView,
    PayPalPaymentFlowView, PayPalSuccessView, PayPalCancelView,
    MemberPaymentHistoryView, EventRegistrationPaymentView,
    InvoicePDFView, BulkInvoicePDFView, TestInvoicePDFView,
    DepartmentInvoiceCreateView, PaymentExportView
)
from payments.views.invoice.due_date_views import InvoiceDueDateView

urlpatterns = [
    # Payment management
    path('payments/', PaymentListView.as_view(), name='payment-list'),
    path('payments/export/', PaymentExportView.as_view(), name='payment-export'),
    path('create/', PaymentCreateView.as_view(), name='payment-create'),
    path('<int:payment_id>/', PaymentDetailView.as_view(), name='payment-detail'),

    # Membership invoice creation
    path('department/invoice/', DepartmentInvoiceCreateView.as_view(), name='department-invoice-create'),

    # PayPal payment processing
    path('paypal/initiate/', InitiatePayPalPaymentView.as_view(), name='paypal-initiate'),
    path('paypal/capture/', CapturePayPalPaymentView.as_view(), name='paypal-capture'),

    # PayPal payment flow (for frontend integration)
    path('paypal/pay/', PayPalPaymentFlowView.as_view(), name='paypal-pay'),
    path('paypal/success/', PayPalSuccessView.as_view(), name='paypal-success'),
    path('paypal/cancel/', PayPalCancelView.as_view(), name='paypal-cancel'),

    # Invoice due date - single endpoint for get/create/update
    path('due-date/', InvoiceDueDateView.as_view(), name='invoice-due-date'),

    # Member payment history
    path('member/<int:member_id>/', MemberPaymentHistoryView.as_view(), name='member-payments'),
    path('member/', MemberPaymentHistoryView.as_view(), name='member-payments-query'),

    # Event registration payment
    path('event/<int:event_id>/', EventRegistrationPaymentView.as_view(), name='event-payments'),
    path('event/', EventRegistrationPaymentView.as_view(), name='event-payments-create'),

    # Invoice PDF generation
    path('invoice/<int:payment_id>/pdf/', InvoicePDFView.as_view(), name='invoice-pdf'),
    path('invoice/bulk-pdf/', BulkInvoicePDFView.as_view(), name='bulk-invoice-pdf'),

    # Test invoice PDF generation
    path('test-invoice/', TestInvoicePDFView.as_view(), name='test-invoice-pdf'),


    path('public/create/', PaymentCreateView.as_view(), name='payment-create'),

]