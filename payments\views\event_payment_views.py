"""
Views for event payment functionality.
"""
from django.db import transaction
from django.utils import timezone
from rest_framework.permissions import IsAuthenticated

from common.views import BaseAPIView
from core.models import Member, EventRegistration
from payments.models import Payment
from payments.services import PaymentService
from payments.serializers import PaymentSerializer


class EventRegistrationPaymentView(BaseAPIView):
    """View for managing event registration payments"""
    permission_classes = [IsAuthenticated]

    def get(self, request, event_id=None, *args, **kwargs):
        """Get payment details for an event registration"""
        if not event_id:
            event_id = request.query_params.get('event_id')

        if not event_id:
            return self.error_response("Event registration ID is required")

        try:
            event = EventRegistration.objects.get(pk=event_id)
        except EventRegistration.DoesNotExist:
            return self.error_response("Event registration not found")

        # Get all payments associated with this event registration
        payments = Payment.objects.filter(event_registration=event).order_by('-date')

        serializer = PaymentSerializer(payments, many=True)
        return self.success_response(serializer.data)

    def post(self, request, *args, **kwargs):
        """Create a payment for an event registration"""
        event_id = request.data.get('event_id')
        if not event_id:
            return self.error_response("Event registration ID is required")

        try:
            event = EventRegistration.objects.get(pk=event_id)
        except EventRegistration.DoesNotExist:
            return self.error_response("Event registration not found")

        payer_id = request.data.get('payer_id')
        try:
            payer = Member.objects.get(pk=payer_id)
        except Member.DoesNotExist:
            return self.error_response("Payer not found")

        # Create the payment using service
        try:
            payment = PaymentService.create_event_payment(
                payer=payer,
                event_registration=event,
                payment_type=request.data.get('payment_type', Payment.PaymentType.PAYPAL),
                status=Payment.PaymentStatus.PENDING,
                due_date=timezone.now().date()
            )

            serializer = PaymentSerializer(payment)
            return self.success_response(serializer.data)

        except Exception as e:
            return self.error_response(f"Error creating payment: {str(e)}")