"""
Views for generating PDF invoices for payments
"""
from datetime import datetime
from django.http import HttpResponse, Http404
from django.utils import timezone
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from common.views import BaseAPIView, APIResponse
from common.utils.pdf_generator import generate_invoice_pdf
from payments.models import Payment
from core.models import Member, EventRegistration


class InvoicePDFView(BaseAPIView):
    """View for generating PDF invoices for payments"""
    permission_classes = []
    
    def get(self, request, payment_id, *args, **kwargs):
        """Generate a PDF invoice for a payment"""
        try:
            payment = Payment.objects.select_related('payer', 'event_registration').get(pk=payment_id)
        except Payment.DoesNotExist:
            return APIResponse(
                message="Payment not found",
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )
        
        # Generate invoice items based on payment type
        invoice_items = self._generate_invoice_items(payment)
        
        # Generate PDF using WeasyPrint
        context = {
            'invoice': payment,
            'items': invoice_items,
            'period_start': datetime(payment.paid_year, 1, 1),
            'period_end': datetime(payment.paid_year, 12, 31),
        }
        filename = f"invoice_{payment.invoice_number}.pdf"
        return generate_invoice_pdf('pdf/invoice.html', context, filename)
    
    def _generate_invoice_items(self, payment):
        """Generate invoice items based on payment type"""
        items = []
        
        if payment.payment_for == Payment.PaymentFor.MEMBERSHIP:
            # Handle membership payment
            covered_members = payment.covered_members.all()
            
            # Add each covered member as an item
            for member in covered_members:
                items.append({
                    'quantity': 1,
                    'description': f"Membership - {member.name} ({member.get_membership_class_display()})",
                    'unit_price': payment.total_amount / len(covered_members) if len(covered_members) > 0 else payment.amount,
                    'amount': payment.total_amount / len(covered_members) if len(covered_members) > 0 else payment.amount
                })
        
        elif payment.payment_for == Payment.PaymentFor.EVENT:
            # Handle event registration payment
            event_reg = payment.event_registration
            
            if event_reg:
                # Add main registration
                items.append({
                    'quantity': event_reg.number_of_participants,
                    'description': f"Event Registration - {event_reg.event.name if event_reg.event else 'Event'} ({event_reg.get_registration_type_display()})",
                    'unit_price': event_reg.base_amount / event_reg.number_of_participants if event_reg.number_of_participants > 0 else event_reg.base_amount,
                    'amount': event_reg.base_amount
                })
                
                # Add guests if any
                if event_reg.number_of_guests > 0:
                    items.append({
                        'quantity': event_reg.number_of_guests,
                        'description': f"Guest Registration - {event_reg.event.name if event_reg.event else 'Event'}",
                        'unit_price': event_reg.guest_amount / event_reg.number_of_guests if event_reg.number_of_guests > 0 else event_reg.guest_amount,
                        'amount': event_reg.guest_amount
                    })
        
        # If no items were generated, add a default item
        if not items:
            items.append({
                'quantity': 1,
                'description': f"Payment - {payment.get_payment_for_display()}",
                'unit_price': payment.amount,
                'amount': payment.amount
            })
        
        return items


class BulkInvoicePDFView(BaseAPIView):
    """View for generating bulk PDF invoices for multiple payments"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request, *args, **kwargs):
        """Generate PDF invoices for multiple payments"""
        payment_ids = request.data.get('payment_ids', [])
        
        if not payment_ids:
            return APIResponse(
                message="No payment IDs provided",
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # Fetch all payments
        payments = Payment.objects.filter(id__in=payment_ids).select_related('payer', 'event_registration')
        
        if not payments:
            return APIResponse(
                message="No valid payments found",
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )
        
        # For now, just return the first payment's PDF
        # In a real implementation, you would merge multiple PDFs
        payment = payments.first()
        invoice_items = self._generate_invoice_items(payment)
        
        # Generate PDF using WeasyPrint
        context = {
            'invoice': payment,
            'items': invoice_items,
            'period_start': datetime(payment.paid_year, 1, 1),
            'period_end': datetime(payment.paid_year, 12, 31),
        }
        filename = f"invoice_{payment.invoice_number}.pdf"
        return generate_invoice_pdf('pdf/invoice.html', context, filename)
    
    def _generate_invoice_items(self, payment):
        """Generate invoice items based on payment type"""
        items = []
        
        if payment.payment_for == Payment.PaymentFor.MEMBERSHIP:
            # Handle membership payment
            covered_members = payment.covered_members.all()
            
            # Add each covered member as an item
            for member in covered_members:
                items.append({
                    'quantity': 1,
                    'description': f"Membership - {member.name} ({member.get_membership_class_display()})",
                    'unit_price': payment.amount / len(covered_members) if len(covered_members) > 0 else payment.amount,
                    'amount': payment.amount / len(covered_members) if len(covered_members) > 0 else payment.amount
                })
        
        elif payment.payment_for == Payment.PaymentFor.EVENT:
            # Handle event registration payment
            event_reg = payment.event_registration
            
            if event_reg:
                # Add main registration
                items.append({
                    'quantity': event_reg.number_of_participants,
                    'description': f"Event Registration - {event_reg.event.name if event_reg.event else 'Event'} ({event_reg.get_registration_type_display()})",
                    'unit_price': event_reg.base_amount / event_reg.number_of_participants if event_reg.number_of_participants > 0 else event_reg.base_amount,
                    'amount': event_reg.base_amount
                })
                
                # Add guests if any
                if event_reg.number_of_guests > 0:
                    items.append({
                        'quantity': event_reg.number_of_guests,
                        'description': f"Guest Registration - {event_reg.event.name if event_reg.event else 'Event'}",
                        'unit_price': event_reg.guest_amount / event_reg.number_of_guests if event_reg.number_of_guests > 0 else event_reg.guest_amount,
                        'amount': event_reg.guest_amount
                    })
        
        # If no items were generated, add a default item
        if not items:
            items.append({
                'quantity': 1,
                'description': f"Payment - {payment.get_payment_for_display()}",
                'unit_price': payment.amount,
                'amount': payment.amount
            })
        
        return items
