"""
Views for payment management.
"""
from django.db import transaction
from django.utils import timezone
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.generics import ListAPIView
from django_filters import rest_framework as filters

from common.views import BaseAPIView, APIResponse
from common.utils import track_activity
from common.pagination import StandardPagination
from common.filters.payment_filters import DynamicFieldsPaymentFilter
from core.models import Member, EventRegistration
from payments.models import Payment, InvoiceDueDate

from payments.serializers import (
    PaymentSerializer, PaymentCreateSerializer, PaymentInputSerializer
)


class PaymentListView(BaseAPIView, ListAPIView):
    """View for listing payments with filtering and pagination"""
    permission_classes = [IsAuthenticated]
    queryset = Payment.objects.all().select_related('payer', 'event_registration')
    serializer_class = PaymentSerializer
    pagination_class = StandardPagination
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = DynamicFieldsPaymentFilter

    def get_queryset(self):
        # Return the base queryset ordered by date (newest first)
        queryset = super().get_queryset().order_by('-date')
        return queryset

    @track_activity(description="Viewed payments list")
    def list(self, request, *args, **kwargs):
        # The filtering is handled automatically by ListAPIView before this method is called
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            paginated_data = self.paginator.get_paginated_response(serializer.data).data
            return APIResponse(
                data=paginated_data,
                message="Payments retrieved successfully",
                status_code=status.HTTP_200_OK
            )

        serializer = self.get_serializer(queryset, many=True)
        return APIResponse(
            data=serializer.data,
            message="Payments retrieved successfully",
            status_code=status.HTTP_200_OK
        )


class PaymentCreateView(BaseAPIView):
    """View for creating payments"""
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        """
        Create a payment for membership or event registration
        """
        serializer = PaymentInputSerializer(data=request.data)
        if not serializer.is_valid():
            return APIResponse(
                message=serializer.errors,
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        validated_data = serializer.validated_data
        payment_for = validated_data.get('payment_for', Payment.PaymentFor.MEMBERSHIP)
        payer = validated_data.get('payer')

        with transaction.atomic():
            # Handle membership payments
            if payment_for == Payment.PaymentFor.MEMBERSHIP:
                covered_members = validated_data.get('covered_members', [])

                # Calculate or use provided amount
                amount = validated_data.get('amount')
                if not amount:
                    # Calculate based on number of members
                    member_fee = validated_data.get('member_fee', 45)  # Default $50 per member
                    amount = len(covered_members) * member_fee

                # Calculate per-member amount and total amount
                per_member_amount = validated_data.get('member_fee', 45)  # Default $50 per member
                total_amount = len(covered_members) * per_member_amount
                print(total_amount)

                # Create a single payment record
                payment = Payment.objects.create(
                    payer=payer,
                    amount=per_member_amount,  # Per-member amount
                    total_amount=total_amount,  # Total amount
                    payment_for=validated_data.get('payment_for', Payment.PaymentFor.MEMBERSHIP),
                    payment_type=validated_data.get('payment_type', Payment.PaymentType.PAYPAL),
                    status=validated_data.get('status', Payment.PaymentStatus.PENDING),
                    paid_year=validated_data.get('paid_year', InvoiceDueDate.objects.first().due_date.year),
                    due_date=validated_data.get('due_date', InvoiceDueDate.objects.first().due_date),
                    notes=validated_data.get('notes', f"Membership payment for {len(covered_members)} member(s)"),
                    po_number=validated_data.get('po_number'),
                    draft=validated_data.get('draft', True),
                    billing_address=validated_data.get('billing_address', '')
                )
                print(covered_members)

                # Add all covered members
                for member_id in covered_members:
                    try:
                        member = Member.objects.get(pk=member_id)
                        payment.covered_members.add(member)
                    except Member.DoesNotExist as e:
                        print(e)
                        # Skip invalid members
                        continue

            # Handle event registration payments
            elif payment_for == Payment.PaymentFor.EVENT:
                event = validated_data.get('event_registration')

                # Create payment record
                # For event payments, we'll set amount and total_amount to the same value
                # since we're not multiplying by covered members count
                payment = Payment.objects.create(
                    payer=payer,
                    amount=event.total_amount,
                    total_amount=event.total_amount,  # Same as amount for event payments
                    payment_for=Payment.PaymentFor.EVENT,
                    event_registration=event,
                    payment_type=validated_data.get('payment_type', Payment.PaymentType.PAYPAL),
                    status=validated_data.get('status', Payment.PaymentStatus.PENDING),
                    due_date=validated_data.get('due_date', timezone.now().date()),
                    notes=validated_data.get('notes', f"Event registration payment for {event.get_full_name()}"),
                    po_number=validated_data.get('po_number'),
                    draft=validated_data.get('draft', True),
                    billing_address=validated_data.get('billing_address', '')
                )

                # Add payer as a covered member
                payment.covered_members.add(payer)

                # If group registration, add all group members as covered members
                if event.group_registration and event.group_members.exists():
                    for member in event.group_members.all():
                        payment.covered_members.add(member)

            response_serializer = PaymentSerializer(payment)
            return APIResponse(
                message="Payment created successfully",
                data=response_serializer.data,
                status_code=status.HTTP_201_CREATED
            )


class PaymentDetailView(BaseAPIView):
    """View for retrieving, updating and deleting a payment"""
    permission_classes = [IsAuthenticated]

    @track_activity(description="Viewed payment details")
    def get(self, request, payment_id, *args, **kwargs):
        """Get payment details"""
        try:
            payment = Payment.objects.select_related('payer', 'event_registration').get(pk=payment_id)
            serializer = PaymentSerializer(payment)
            return APIResponse(
                message="Payment retrieved successfully",
                data=serializer.data,
                status_code=status.HTTP_200_OK
            )
        except Payment.DoesNotExist:
            return APIResponse(
                message="Payment not found",
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )

    def put(self, request, payment_id, *args, **kwargs):
        """Update payment details"""
        try:
            payment = Payment.objects.get(pk=payment_id)

            # Create a copy of the request data for the serializer
            serializer_data = request.data.copy()

            # Pass the serializer data to the serializer
            serializer = PaymentCreateSerializer(payment, data=serializer_data, partial=True)

            if serializer.is_valid():
                # Save the payment with the updated data
                payment = serializer.save()

                # Return the updated payment
                return APIResponse(
                    message="Payment updated successfully",
                    data=PaymentSerializer(payment).data,
                    status_code=status.HTTP_200_OK
                )
            return APIResponse(
                message=serializer.errors,
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except Payment.DoesNotExist:
            return APIResponse(
                message="Payment not found",
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )

    def delete(self, request, payment_id, *args, **kwargs):
        """Delete a payment"""
        try:
            payment = Payment.objects.get(pk=payment_id)
            payment.delete()
            return APIResponse(
                message="Payment deleted successfully",
                data=None,
                status_code=status.HTTP_200_OK
            )
        except Payment.DoesNotExist:
            return APIResponse(
                message="Payment not found",
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )


class DepartmentInvoiceCreateView(BaseAPIView):
    """View for creating department invoices"""
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        """
        Create a membership invoice for one or more members
        Expected request data:
        {
            "member_ids": [1, 2, 3],  # List of member IDs
            "due_date": "2024-03-20"  # Due date for the invoice
        }
        """
        member_ids = request.data.get('member_ids', [])
        due_date = request.data.get('due_date')

        if not member_ids:
            return APIResponse(
                message="At least one member ID is required",
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        if not due_date:
            return APIResponse(
                message="Due date is required",
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Validate all members exist
            members = Member.objects.filter(id__in=member_ids)
            if len(members) != len(member_ids):
                return APIResponse(
                    message="One or more member IDs are invalid",
                    data=None,
                    status_code=status.HTTP_400_BAD_REQUEST
                )

            with transaction.atomic():
                # First member is the payer, all members are covered members
                payer = members.first()
                amount = len(members) * 45  # $45 per member

                # Create the payment record
                payment = Payment.objects.create(
                    payer=payer,
                    amount=45,  # Per-member amount
                    total_amount=amount,  # Total amount (45 * number of members)
                    payment_for=Payment.PaymentFor.MEMBERSHIP,
                    payment_type=Payment.PaymentType.CASH,
                    status=Payment.PaymentStatus.INVOICED,
                    due_date=due_date,
                    notes=f"Membership fee for {len(members)} member(s)",
                    draft=False
                )

                # Add all members as covered members
                payment.covered_members.set(members)

                response_serializer = PaymentSerializer(payment)
                return APIResponse(
                    message="Membership invoice created successfully",
                    data=response_serializer.data,
                    status_code=status.HTTP_201_CREATED
                )

        except Exception as e:
            return APIResponse(
                message=str(e),
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )