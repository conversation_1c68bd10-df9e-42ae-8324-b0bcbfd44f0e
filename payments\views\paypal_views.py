"""
Views for PayPal payment processing.
Updated to use PayPalService for improved error handling and maintainability.
"""
import logging
from django.utils import timezone
from rest_framework.permissions import IsAuthenticated

from common.views import BaseAPIView
from core.models import Member
from payments.models import Payment
from payments.serializers import PaymentSerializer, PayPalPaymentSerializer, PayPalCaptureSerializer
from payments.services import PayPalService, PaymentService
from payments.services.paypal_service import PayPalAPIError

logger = logging.getLogger(__name__)


class InitiatePayPalPaymentView(BaseAPIView):
    """View for initiating PayPal payments from existing payment records"""
    permission_classes = [IsAuthenticated]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.paypal_service = PayPalService()

    def post(self, request, *args, **kwargs):
        """Initiate a PayPal payment for an existing payment record"""
        serializer = PayPalPaymentSerializer(data=request.data)
        if not serializer.is_valid():
            return self.error_response(serializer.errors)

        validated_data = serializer.validated_data
        payment = validated_data['payment']
        return_url = validated_data['return_url']
        cancel_url = validated_data['cancel_url']

        try:
            # Generate description using service
            description = PaymentService.get_payment_description(payment)

            # Create PayPal order using service
            reference_id = str(payment.pk)
            paypal_order = self.paypal_service.create_order(
                amount=float(payment.amount),
                description=description,
                reference_id=reference_id,
                return_url=return_url,
                cancel_url=cancel_url
            )

            # Update payment with PayPal information
            payment.payment_type = Payment.PaymentType.PAYPAL
            payment.paypal_order_id = paypal_order.get('id')
            payment.paypal_response = paypal_order
            payment.save()

            # Get the approval URL
            approval_url = next((link['href'] for link in paypal_order.get('links', [])
                               if link['rel'] == 'approve'), None)

            return self.success_response({
                "payment_id": payment.pk,
                "amount": payment.amount,
                "approval_url": approval_url,
                "order_id": paypal_order.get('id')
            })

        except PayPalAPIError as e:
            logger.error(f"PayPal API error for payment {payment.pk}: {str(e)}")
            return self.error_response(f"PayPal error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error initiating PayPal payment {payment.pk}: {str(e)}")
            return self.error_response("Failed to create PayPal order")


class CapturePayPalPaymentView(BaseAPIView):
    """View for capturing approved PayPal payments"""
    permission_classes = [IsAuthenticated]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.paypal_service = PayPalService()

    def post(self, request, *args, **kwargs):
        """Capture an approved PayPal payment"""
        serializer = PayPalCaptureSerializer(data=request.data)
        if not serializer.is_valid():
            return self.error_response(serializer.errors)

        validated_data = serializer.validated_data
        payment = validated_data['payment']
        order_id = validated_data['order_id']

        try:
            # Capture the payment using service
            capture_result = self.paypal_service.capture_payment(order_id)

            # Update payment record
            payment.payment_date = timezone.now().date()
            payment.paypal_response = capture_result

            # Extract PayPal transaction details
            if 'purchase_units' in capture_result:
                for unit in capture_result.get('purchase_units', []):
                    if 'payments' in unit and 'captures' in unit['payments']:
                        for capture in unit['payments']['captures']:
                            payment.transaction_id = capture.get('id')
                            payment.paypal_payment_status = capture.get('status')

                            # Get fee details if available
                            if 'seller_receivable_breakdown' in capture:
                                breakdown = capture['seller_receivable_breakdown']
                                if 'paypal_fee' in breakdown:
                                    payment.paypal_fee = breakdown['paypal_fee']['value']

            # Update payment status using service (handles related payments)
            PaymentService.update_payment_status(payment, Payment.PaymentStatus.SUCCESS)

            # If this is an event registration payment, update the event registration status
            if payment.payment_for == Payment.PaymentFor.EVENT and payment.event_registration:
                event_registration = payment.event_registration
                event_registration.payment_status = 'COMPLETED'
                event_registration.save()

            return self.success_response({
                "payment_id": payment.pk,
                "status": payment.status,
                "transaction_id": payment.transaction_id
            })

        except PayPalAPIError as e:
            logger.error(f"PayPal API error capturing payment {payment.pk}: {str(e)}")
            return self.error_response(f"PayPal error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error capturing PayPal payment {payment.pk}: {str(e)}")
            return self.error_response("Failed to capture PayPal payment")