"""
Views for PayPal payment processing.
"""
import json
import logging
import requests
from django.conf import settings
from django.utils import timezone
from rest_framework.permissions import IsAuthenticated

from common.views import BaseAPIView
from core.models import Member
from payments.models import Payment
from payments.serializers import PaymentSerializer, PayPalPaymentSerializer, PayPalCaptureSerializer

logger = logging.getLogger(__name__)

# PayPal API endpoints
PAYPAL_BASE_URL = 'https://api-m.sandbox.paypal.com' if settings.DEBUG else 'https://api-m.paypal.com'
PAYPAL_OAUTH_URL = f'{PAYPAL_BASE_URL}/v1/oauth2/token'
PAYPAL_ORDER_URL = f'{PAYPAL_BASE_URL}/v2/checkout/orders'


class InitiatePayPalPaymentView(BaseAPIView):
    """View for initiating PayPal payments from existing payment records"""
    permission_classes = [IsAuthenticated]
    
    def get_paypal_access_token(self):
        """Get PayPal OAuth access token"""
        try:
            client_id = settings.PAYPAL_CLIENT_ID
            client_secret = settings.PAYPAL_CLIENT_SECRET
            
            response = requests.post(
                PAYPAL_OAUTH_URL,
                auth=(client_id, client_secret),
                headers={'Accept': 'application/json', 'Accept-Language': 'en_US'},
                data={'grant_type': 'client_credentials'}
            )
            
            response_data = response.json()
            return response_data.get('access_token')
        except Exception as e:
            logger.error(f"Error getting PayPal access token: {str(e)}")
            return None
            
    def create_paypal_order(self, amount, description, reference_id, return_url, cancel_url):
        """Create PayPal order"""
        access_token = self.get_paypal_access_token()
        if not access_token:
            return None
            
        payload = {
            "intent": "CAPTURE",
            "purchase_units": [
                {
                    "reference_id": reference_id,
                    "description": description,
                    "amount": {
                        "currency_code": "USD",
                        "value": str(amount)
                    }
                }
            ],
            "application_context": {
                "return_url": return_url,
                "cancel_url": cancel_url
            }
        }
        
        try:
            response = requests.post(
                PAYPAL_ORDER_URL,
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {access_token}'
                },
                data=json.dumps(payload)
            )
            
            return response.json()
        except Exception as e:
            logger.error(f"Error creating PayPal order: {str(e)}")
            return None
    
    def post(self, request, *args, **kwargs):
        """Initiate a PayPal payment for an existing payment record"""
        serializer = PayPalPaymentSerializer(data=request.data)
        if not serializer.is_valid():
            return self.error_response(serializer.errors)
            
        validated_data = serializer.validated_data
        payment = validated_data['payment']
        return_url = validated_data['return_url']
        cancel_url = validated_data['cancel_url']
            
        # Create description based on payment type
        if payment.payment_for == Payment.PaymentFor.EVENT:
            if payment.event_registration:
                description = f"Event Registration: {payment.event_registration.get_full_name()}"
            else:
                description = "Event Registration Payment"
        else:
            members_count = payment.covered_members.count()
            description = f"Membership Payment for {members_count} member(s)"
            
        # Create PayPal order
        reference_id = str(payment.pk)
        paypal_order = self.create_paypal_order(
            amount=payment.amount,
            description=description,
            reference_id=reference_id,
            return_url=return_url,
            cancel_url=cancel_url
        )
        
        if not paypal_order:
            return self.error_response("Failed to create PayPal order")
            
        # Update payment with PayPal information
        payment.payment_type = Payment.PaymentType.PAYPAL
        payment.paypal_order_id = paypal_order.get('id')
        payment.paypal_response = paypal_order
        payment.save()
        
        # Get the approval URL
        approval_url = next((link['href'] for link in paypal_order.get('links', []) 
                           if link['rel'] == 'approve'), None)
                           
        return self.success_response({
            "payment_id": payment.pk,
            "amount": payment.amount,
            "approval_url": approval_url,
            "order_id": paypal_order.get('id')
        })


class CapturePayPalPaymentView(BaseAPIView):
    """View for capturing approved PayPal payments"""
    permission_classes = [IsAuthenticated]
    
    def capture_paypal_payment(self, order_id):
        """Capture an approved PayPal payment"""
        try:
            client_id = settings.PAYPAL_CLIENT_ID
            client_secret = settings.PAYPAL_CLIENT_SECRET
            
            # Get access token
            token_response = requests.post(
                PAYPAL_OAUTH_URL,
                auth=(client_id, client_secret),
                headers={'Accept': 'application/json', 'Accept-Language': 'en_US'},
                data={'grant_type': 'client_credentials'}
            )
            
            access_token = token_response.json().get('access_token')
            if not access_token:
                return None
            
            # Capture the payment
            capture_url = f"{PAYPAL_ORDER_URL}/{order_id}/capture"
            response = requests.post(
                capture_url,
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {access_token}'
                }
            )
            
            return response.json()
        except Exception as e:
            logger.error(f"Error capturing PayPal payment: {str(e)}")
            return None
    
    def post(self, request, *args, **kwargs):
        """Capture an approved PayPal payment"""
        serializer = PayPalCaptureSerializer(data=request.data)
        if not serializer.is_valid():
            return self.error_response(serializer.errors)
            
        validated_data = serializer.validated_data
        payment = validated_data['payment']
        order_id = validated_data['order_id']
        
        # Capture the payment
        capture_result = self.capture_paypal_payment(order_id)
        if not capture_result:
            return self.error_response("Failed to capture PayPal payment")
            
        # Update payment record
        payment.status = Payment.PaymentStatus.SUCCESS
        payment.payment_date = timezone.now().date()
        payment.paypal_response = capture_result
        
        # Extract PayPal transaction details
        if 'purchase_units' in capture_result:
            for unit in capture_result.get('purchase_units', []):
                if 'payments' in unit and 'captures' in unit['payments']:
                    for capture in unit['payments']['captures']:
                        payment.transaction_id = capture.get('id')
                        payment.paypal_payment_status = capture.get('status')
                        
                        # Get fee details if available
                        if 'seller_receivable_breakdown' in capture:
                            breakdown = capture['seller_receivable_breakdown']
                            if 'paypal_fee' in breakdown:
                                payment.paypal_fee = breakdown['paypal_fee']['value']
        
        payment.save()
        
        # If this is an event registration payment, update the event registration status
        if payment.payment_for == Payment.PaymentFor.EVENT and payment.event_registration:
            event_registration = payment.event_registration
            event_registration.payment_status = 'COMPLETED'
            event_registration.save()
            
        return self.success_response({
            "payment_id": payment.pk,
            "status": payment.status,
            "transaction_id": payment.transaction_id
        }) 