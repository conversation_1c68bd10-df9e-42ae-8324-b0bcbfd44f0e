"""
Views for PayPal payment processing.
Complete PayPal payment flow with initiate, success, and cancel endpoints.
"""
import logging
from django.utils import timezone
from django.http import HttpResponseRedirect
from rest_framework.permissions import IsAuthenticated
from rest_framework import status

from common.views import BaseAPIView, APIResponse
from core.models import Member
from payments.models import Payment
from payments.serializers import PaymentSerializer, PayPalPaymentSerializer, PayPalCaptureSerializer
from payments.services import PayPalService, PaymentService
from payments.services.paypal_service import PayPalAPIError
from payments.config import SuccessMessages, ErrorMessages

logger = logging.getLogger(__name__)


class InitiatePayPalPaymentView(BaseAPIView):
    """View for initiating PayPal payments from existing payment records"""
    permission_classes = [IsAuthenticated]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.paypal_service = PayPalService()

    def post(self, request, *args, **kwargs):
        """Initiate a PayPal payment for an existing payment record"""
        serializer = PayPalPaymentSerializer(data=request.data)
        if not serializer.is_valid():
            return self.error_response(serializer.errors)

        validated_data = serializer.validated_data
        payment = validated_data['payment']
        return_url = validated_data['return_url']
        cancel_url = validated_data['cancel_url']

        try:
            # Generate description using service
            description = PaymentService.get_payment_description(payment)

            # Create PayPal order using service
            reference_id = str(payment.pk)
            paypal_order = self.paypal_service.create_order(
                amount=float(payment.amount),
                description=description,
                reference_id=reference_id,
                return_url=return_url,
                cancel_url=cancel_url
            )

            # Update payment with PayPal information
            payment.payment_type = Payment.PaymentType.PAYPAL
            payment.paypal_order_id = paypal_order.get('id')
            payment.paypal_response = paypal_order
            payment.save()

            # Get the approval URL
            approval_url = next((link['href'] for link in paypal_order.get('links', [])
                               if link['rel'] == 'approve'), None)

            return self.success_response({
                "payment_id": payment.pk,
                "amount": payment.amount,
                "approval_url": approval_url,
                "order_id": paypal_order.get('id')
            })

        except PayPalAPIError as e:
            logger.error(f"PayPal API error for payment {payment.pk}: {str(e)}")
            return self.error_response(f"PayPal error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error initiating PayPal payment {payment.pk}: {str(e)}")
            return self.error_response("Failed to create PayPal order")


class CapturePayPalPaymentView(BaseAPIView):
    """View for capturing approved PayPal payments"""
    permission_classes = [IsAuthenticated]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.paypal_service = PayPalService()

    def post(self, request, *args, **kwargs):
        """Capture an approved PayPal payment"""
        serializer = PayPalCaptureSerializer(data=request.data)
        if not serializer.is_valid():
            return self.error_response(serializer.errors)

        validated_data = serializer.validated_data
        payment = validated_data['payment']
        order_id = validated_data['order_id']

        try:
            # Capture the payment using service
            capture_result = self.paypal_service.capture_payment(order_id)

            # Update payment record
            payment.payment_date = timezone.now().date()
            payment.paypal_response = capture_result

            # Extract PayPal transaction details
            if 'purchase_units' in capture_result:
                for unit in capture_result.get('purchase_units', []):
                    if 'payments' in unit and 'captures' in unit['payments']:
                        for capture in unit['payments']['captures']:
                            payment.transaction_id = capture.get('id')
                            payment.paypal_payment_status = capture.get('status')

                            # Get fee details if available
                            if 'seller_receivable_breakdown' in capture:
                                breakdown = capture['seller_receivable_breakdown']
                                if 'paypal_fee' in breakdown:
                                    payment.paypal_fee = breakdown['paypal_fee']['value']

            # Update payment status using service (handles related payments)
            PaymentService.update_payment_status(payment, Payment.PaymentStatus.SUCCESS)

            # If this is an event registration payment, update the event registration status
            if payment.payment_for == Payment.PaymentFor.EVENT and payment.event_registration:
                event_registration = payment.event_registration
                event_registration.payment_status = 'COMPLETED'
                event_registration.save()

            return self.success_response({
                "payment_id": payment.pk,
                "status": payment.status,
                "transaction_id": payment.transaction_id
            })

        except PayPalAPIError as e:
            logger.error(f"PayPal API error capturing payment {payment.pk}: {str(e)}")
            return self.error_response(f"PayPal error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error capturing PayPal payment {payment.pk}: {str(e)}")
            return self.error_response("Failed to capture PayPal payment")


class PayPalPaymentFlowView(BaseAPIView):
    """Complete PayPal payment flow for frontend integration"""
    permission_classes = [IsAuthenticated]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.paypal_service = PayPalService()

    def post(self, request, *args, **kwargs):
        """
        Initiate PayPal payment flow
        Expected request data:
        {
            "payment_id": 123,
            "frontend_base_url": "https://yourfrontend.com"
        }
        """
        payment_id = request.data.get('payment_id')
        frontend_base_url = request.data.get('frontend_base_url', 'http://localhost:3000')

        if not payment_id:
            return APIResponse(
                message="Payment ID is required",
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        try:
            payment = Payment.objects.get(pk=payment_id)
        except Payment.DoesNotExist:
            return APIResponse(
                message=ErrorMessages.PAYMENT_NOT_FOUND,
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )

        # Generate return and cancel URLs
        return_url = f"{request.build_absolute_uri('/api/payments/paypal/success/')}?payment_id={payment_id}"
        cancel_url = f"{request.build_absolute_uri('/api/payments/paypal/cancel/')}?payment_id={payment_id}"

        try:
            # Generate description using service
            description = PaymentService.get_payment_description(payment)

            # Create PayPal order using service
            reference_id = str(payment.pk)
            paypal_order = self.paypal_service.create_order(
                amount=float(payment.total_amount or payment.amount),
                description=description,
                reference_id=reference_id,
                return_url=return_url,
                cancel_url=cancel_url
            )

            # Update payment with PayPal information
            payment.payment_type = Payment.PaymentType.PAYPAL
            payment.paypal_order_id = paypal_order.get('id')
            payment.paypal_response = paypal_order
            payment.save()

            # Get the approval URL
            approval_url = next((link['href'] for link in paypal_order.get('links', [])
                               if link['rel'] == 'approve'), None)

            if not approval_url:
                return APIResponse(
                    message="Failed to get PayPal approval URL",
                    data=None,
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            return APIResponse(
                message=SuccessMessages.PAYPAL_ORDER_CREATED,
                data={
                    "payment_id": payment.pk,
                    "amount": float(payment.total_amount or payment.amount),
                    "approval_url": approval_url,
                    "order_id": paypal_order.get('id'),
                    "description": description
                },
                status_code=status.HTTP_201_CREATED
            )

        except PayPalAPIError as e:
            logger.error(f"PayPal API error for payment {payment.pk}: {str(e)}")
            return APIResponse(
                message=f"PayPal error: {str(e)}",
                data=None,
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Unexpected error initiating PayPal payment {payment.pk}: {str(e)}")
            return APIResponse(
                message="Failed to create PayPal order",
                data=None,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PayPalSuccessView(BaseAPIView):
    """Handle successful PayPal payment callback"""
    permission_classes = []  # PayPal callbacks don't include authentication

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.paypal_service = PayPalService()

    def get(self, request, *args, **kwargs):
        """Handle PayPal success callback"""
        payment_id = request.GET.get('payment_id')
        paypal_order_id = request.GET.get('token')  # PayPal sends order ID as 'token'
        payer_id = request.GET.get('PayerID')

        if not payment_id or not paypal_order_id:
            logger.error(f"Missing parameters in PayPal success callback: payment_id={payment_id}, token={paypal_order_id}")
            return HttpResponseRedirect(f"{request.scheme}://{request.get_host()}/payment-error?error=missing_parameters")

        try:
            payment = Payment.objects.get(pk=payment_id)
        except Payment.DoesNotExist:
            logger.error(f"Payment not found in success callback: {payment_id}")
            return HttpResponseRedirect(f"{request.scheme}://{request.get_host()}/payment-error?error=payment_not_found")

        try:
            # Capture the payment using PayPal service
            capture_result = self.paypal_service.capture_payment(paypal_order_id)

            # Update payment record
            payment.payment_date = timezone.now().date()
            payment.paypal_response = capture_result
            payment.paypal_order_id = paypal_order_id

            # Extract PayPal transaction details
            if 'purchase_units' in capture_result:
                for unit in capture_result.get('purchase_units', []):
                    if 'payments' in unit and 'captures' in unit['payments']:
                        for capture in unit['payments']['captures']:
                            payment.transaction_id = capture.get('id')
                            payment.paypal_payment_status = capture.get('status')

                            # Get fee details if available
                            if 'seller_receivable_breakdown' in capture:
                                breakdown = capture['seller_receivable_breakdown']
                                if 'paypal_fee' in breakdown:
                                    payment.paypal_fee = breakdown['paypal_fee']['value']

            # Update payment status using service (handles related payments)
            PaymentService.update_payment_status(payment, Payment.PaymentStatus.SUCCESS)

            # If this is an event registration payment, update the event registration status
            if payment.payment_for == Payment.PaymentFor.EVENT and payment.event_registration:
                event_registration = payment.event_registration
                event_registration.payment_status = 'COMPLETED'
                event_registration.save()

            logger.info(f"PayPal payment successful: payment_id={payment_id}, transaction_id={payment.transaction_id}")

            # Redirect to frontend success page
            return HttpResponseRedirect(f"{request.scheme}://{request.get_host()}/payment-success?payment_id={payment_id}&transaction_id={payment.transaction_id}")

        except PayPalAPIError as e:
            logger.error(f"PayPal API error in success callback for payment {payment_id}: {str(e)}")
            return HttpResponseRedirect(f"{request.scheme}://{request.get_host()}/payment-error?error=paypal_api_error")
        except Exception as e:
            logger.error(f"Unexpected error in PayPal success callback for payment {payment_id}: {str(e)}")
            return HttpResponseRedirect(f"{request.scheme}://{request.get_host()}/payment-error?error=processing_error")


class PayPalCancelView(BaseAPIView):
    """Handle cancelled PayPal payment callback"""
    permission_classes = []  # PayPal callbacks don't include authentication

    def get(self, request, *args, **kwargs):
        """Handle PayPal cancel callback"""
        payment_id = request.GET.get('payment_id')
        paypal_order_id = request.GET.get('token')  # PayPal sends order ID as 'token'

        if not payment_id:
            logger.warning(f"Missing payment_id in PayPal cancel callback")
            return HttpResponseRedirect(f"{request.scheme}://{request.get_host()}/payment-cancelled")

        try:
            payment = Payment.objects.get(pk=payment_id)

            # Update payment status to cancelled
            PaymentService.update_payment_status(payment, Payment.PaymentStatus.CANCELLED)

            logger.info(f"PayPal payment cancelled: payment_id={payment_id}, order_id={paypal_order_id}")

            # Redirect to frontend cancel page
            return HttpResponseRedirect(f"{request.scheme}://{request.get_host()}/payment-cancelled?payment_id={payment_id}")

        except Payment.DoesNotExist:
            logger.error(f"Payment not found in cancel callback: {payment_id}")
            return HttpResponseRedirect(f"{request.scheme}://{request.get_host()}/payment-cancelled?error=payment_not_found")
        except Exception as e:
            logger.error(f"Error in PayPal cancel callback for payment {payment_id}: {str(e)}")
            return HttpResponseRedirect(f"{request.scheme}://{request.get_host()}/payment-cancelled?error=processing_error")