"""
Test views for downloading invoice PDFs using different libraries
"""
from django.http import Http404
from rest_framework import status
from rest_framework.views import APIView

from common.views import BaseAPIView, APIResponse
from common.utils.pdf_generator import (
    render_to_pdf, 
    generate_pdf_with_weasyprint,
    generate_invoice_pdf_with_reportlab
)
from payments.models import Payment
from datetime import datetime


class BaseTestInvoicePDFView(BaseAPIView):
    """Base test view for downloading invoice PDFs"""
    permission_classes = []
    
    def _get_first_payment(self):
        """Get the first available payment"""
        payment = Payment.objects.select_related('payer', 'event_registration').first()
        
        if not payment:
            raise Http404("No payments found in the system")
        
        return payment
    
    def _generate_invoice_items(self, payment):
        """Generate invoice items based on payment type"""
        items = []
        
        if payment.payment_for == Payment.PaymentFor.MEMBERSHIP:
            # Handle membership payment
            covered_members = payment.covered_members.all()
            
            # Add each covered member as an item
            for member in covered_members:
                items.append({
                    'quantity': 1,
                    'description': f"Membership - {member.name} ({member.get_membership_class_display()})",
                    'unit_price': payment.amount / len(covered_members) if len(covered_members) > 0 else payment.amount,
                    'amount': payment.amount / len(covered_members) if len(covered_members) > 0 else payment.amount
                })
        
        elif payment.payment_for == Payment.PaymentFor.EVENT:
            # Handle event registration payment
            event_reg = payment.event_registration
            
            if event_reg:
                # Add main registration
                items.append({
                    'quantity': event_reg.number_of_participants,
                    'description': f"Event Registration - {event_reg.event.name if event_reg.event else 'Event'} ({event_reg.get_registration_type_display()})",
                    'unit_price': event_reg.base_amount / event_reg.number_of_participants if event_reg.number_of_participants > 0 else event_reg.base_amount,
                    'amount': event_reg.base_amount
                })
                
                # Add guests if any
                if event_reg.number_of_guests > 0:
                    items.append({
                        'quantity': event_reg.number_of_guests,
                        'description': f"Guest Registration - {event_reg.event.name if event_reg.event else 'Event'}",
                        'unit_price': event_reg.guest_amount / event_reg.number_of_guests if event_reg.number_of_guests > 0 else event_reg.guest_amount,
                        'amount': event_reg.guest_amount
                    })
        
        # If no items were generated, add a default item
        if not items:
            items.append({
                'quantity': 1,
                'description': f"Payment - {payment.get_payment_for_display()}",
                'unit_price': payment.amount,
                'amount': payment.amount
            })
        
        return items


class TestInvoicePDFReportLabView(BaseTestInvoicePDFView):
    """Test view for downloading invoice PDF using ReportLab"""
    
    def get(self, request, *args, **kwargs):
        """Download invoice PDF using ReportLab"""
        try:
            payment = self._get_first_payment()
            invoice_items = self._generate_invoice_items(payment)
            
            # Generate PDF using ReportLab
            filename = f"test_invoice_reportlab_{payment.invoice_number}.pdf"
            return generate_invoice_pdf_with_reportlab(payment, invoice_items, filename)
        except Http404 as e:
            return APIResponse(
                message=str(e),
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )


class TestInvoicePDFWeasyPrintView(BaseTestInvoicePDFView):
    """Test view for downloading invoice PDF using WeasyPrint"""
    
    def get(self, request, *args, **kwargs):
        """Download invoice PDF using WeasyPrint"""
        try:
            payment = self._get_first_payment()
            invoice_items = self._generate_invoice_items(payment)
            
            # Generate context for the template
            context = {
                'invoice': payment,
                'items': invoice_items,
                'period_start': datetime(payment.paid_year, 1, 1),
                'period_end': datetime(payment.paid_year, 12, 31),
            }
            
            # Generate PDF using WeasyPrint
            filename = f"test_invoice_weasyprint_{payment.invoice_number}.pdf"
            return generate_pdf_with_weasyprint('pdf/invoice.html', context, filename)
        except Http404 as e:
            return APIResponse(
                message=str(e),
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )


class TestInvoicePDFXhtml2PDFView(BaseTestInvoicePDFView):
    """Test view for downloading invoice PDF using xhtml2pdf"""
    
    def get(self, request, *args, **kwargs):
        """Download invoice PDF using xhtml2pdf"""
        try:
            payment = self._get_first_payment()
            invoice_items = self._generate_invoice_items(payment)
            
            # Generate context for the template
            context = {
                'invoice': payment,
                'items': invoice_items,
                'period_start': datetime(payment.paid_year, 1, 1),
                'period_end': datetime(payment.paid_year, 12, 31),
            }
            
            # Generate PDF using xhtml2pdf
            pdf = render_to_pdf('pdf/invoice.html', context)
            if pdf:
                filename = f"test_invoice_xhtml2pdf_{payment.invoice_number}.pdf"
                pdf['Content-Disposition'] = f'attachment; filename="{filename}"'
                return pdf
            
            return APIResponse(
                message="Error generating PDF with xhtml2pdf",
                data=None,
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Http404 as e:
            return APIResponse(
                message=str(e),
                data=None,
                status_code=status.HTTP_404_NOT_FOUND
            )
